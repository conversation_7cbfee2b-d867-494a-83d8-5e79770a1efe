-- section SC<PERSON>EMA
DROP SCHEMA IF EXISTS app_access CASCADE
;

CREATE SCHEMA IF NOT EXISTS app_access
;

GRANT USAGE ON SCHEMA app_access TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_access TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_access TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_access TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_access
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_access
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_access
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

-- !section
-- section FUNCTIONS
-- anchor has_capability
CREATE OR REPLACE FUNCTION app_access.has_capability (
  capability_name TEXT,
  p_user_id UUID DEFAULT NULL
) R<PERSON>URNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_access.user_role ur
    JOIN app_access.role_capability rc ON ur.role_id = rc.role_id
    JOIN app_access.capability c ON rc.capability_id = c.id
    WHERE ur.user_id = COALESCE(p_user_id, auth.uid()) AND c.name = capability_name
  );
END;
$$
;

-- anchor has_role
CREATE OR REPLACE FUNCTION app_access.has_role (role_name TEXT) RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM app_access.user_role ur
    JOIN app_access.role r ON ur.role_id = r.id
    WHERE ur.user_id = (SELECT auth.uid()) AND r.name = role_name
  );
END;
$$
;

-- anchor define_role_capability
CREATE OR REPLACE FUNCTION app_access.define_role_capability (
  v_role_name TEXT,
  v_capability_names TEXT[]
) RETURNS BOOLEAN LANGUAGE plpgsql AS $$
DECLARE
  v_role_id INT;
  v_capability_name TEXT;
  v_capability_id INT;
BEGIN
  IF current_user != 'postgres' THEN
    RAISE EXCEPTION 'You do not have permission to define role capabilities.';
  END IF;

  -- Check if the role exists
  SELECT
    id INTO v_role_id
  FROM
    app_access.role
  WHERE
    NAME = v_role_name;

  IF v_role_id IS NULL THEN
    RETURN FALSE; -- Role not found
  END IF;

  -- Loop through each capability name in the array
  FOREACH v_capability_name IN ARRAY v_capability_names LOOP
    -- Create the capability if it does not exist
    INSERT INTO
      app_access.capability (NAME)
    VALUES
      (v_capability_name) ON CONFLICT (NAME)
    DO NOTHING;

    -- Get the capability ID
    SELECT
      id INTO v_capability_id
    FROM
      app_access.capability
    WHERE
      NAME = v_capability_name;

    -- If capability ID is retrieved, bind it to the role
    IF v_capability_id IS NOT NULL THEN
      INSERT INTO
        app_access.role_capability (role_id, capability_id)
      VALUES
        (v_role_id, v_capability_id) ON CONFLICT (role_id, capability_id)
      DO NOTHING;
    END IF;
  END LOOP;

  RETURN TRUE;
END;
$$
;

-- anchor assign_role_to_user
CREATE OR REPLACE FUNCTION app_access.assign_role_to_user (
  v_user_id UUID,
  v_role_name TEXT
) RETURNS BOOLEAN LANGUAGE plpgsql AS $$ DECLARE
  v_role_id INT;
BEGIN
  SELECT
    id INTO v_role_id
  FROM
    app_access.role
  WHERE
    NAME = v_role_name;

  -- If the role exists, assign it to the user
  IF v_role_id IS NOT NULL THEN
    -- Assign the new role to the user
    INSERT INTO
      app_access.user_role (user_id, role_id)
    VALUES
      (v_user_id, v_role_id) ON CONFLICT (user_id, role_id) DO NOTHING;

    RETURN TRUE;
  ELSE
    RETURN FALSE; -- Role not found
  END IF;
END;
$$
;

-- anchor revoke_role_from_user
CREATE OR REPLACE FUNCTION app_access.revoke_role_from_user (
  v_user_id UUID,
  v_role_name TEXT
) RETURNS BOOLEAN LANGUAGE plpgsql AS $$ DECLARE
  v_role_id INT;
BEGIN
  SELECT
    id INTO v_role_id
  FROM
    app_access.role
  WHERE
    NAME = v_role_name;

  -- If the role exists, remove it from the user
  IF v_role_id IS NOT NULL THEN
    DELETE FROM app_access.user_role
    WHERE user_id = v_user_id AND role_id = v_role_id;

    RETURN TRUE;
  ELSE
    RETURN FALSE; -- Role not found
  END IF;
END;
$$
;

-- !section
-- section TRIGGER FUNCTIONS
-- anchor assign_customer_role_on_new_user
CREATE OR REPLACE FUNCTION app_access.assign_customer_role_on_new_user () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
BEGIN
  PERFORM app_access.assign_role_to_user(NEW.id, 'customer');
  RETURN NEW;
END;
$$
;

-- !section
-- section TABLES
-- anchor role
CREATE TABLE app_access.role (
  id INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  NAME TEXT NOT NULL UNIQUE CHECK (LENGTH(NAME) <= 50),
  description TEXT CHECK (LENGTH(description) <= 255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor capability
CREATE TABLE app_access.capability (
  id INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY,
  NAME TEXT NOT NULL UNIQUE CHECK (LENGTH(NAME) <= 50),
  description TEXT CHECK (LENGTH(description) <= 255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
;

-- anchor role_capability
CREATE TABLE app_access.role_capability (
  role_id INT REFERENCES app_access.role (id) ON DELETE CASCADE,
  capability_id INT REFERENCES app_access.capability (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (role_id, capability_id)
)
;

-- anchor user_role
CREATE TABLE app_access.user_role (
  user_id UUID REFERENCES auth.users (id) ON DELETE CASCADE,
  role_id INT REFERENCES app_access.role (id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, role_id)
)
;

-- !section
-- section FUNCTIONS
-- anchor notify_role_change
CREATE OR REPLACE FUNCTION app_access.notify_role_change () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_role_name TEXT;
BEGIN
  IF TG_OP = 'INSERT' THEN
    -- Get role name for new assignment
    SELECT name INTO v_role_name
    FROM app_access.role
    WHERE id = NEW.role_id;

    PERFORM app_account.create_notification(
      'access.role.assigned',
      NEW.user_id,
      'notifications.access.role.assigned.title',
      'notifications.access.role.assigned.message',
      jsonb_build_object(
        'role_name', v_role_name,
        'assigned_at', NEW.created_at
      ),
      '/account/roles'
    );
  ELSIF TG_OP = 'DELETE' THEN
    -- Get role name for removed assignment
    SELECT name INTO v_role_name
    FROM app_access.role
    WHERE id = OLD.role_id;

    PERFORM app_account.create_notification(
      'access.role.removed',
      OLD.user_id,
      'notifications.access.role.removed.title',
      'notifications.access.role.removed.message',
      jsonb_build_object(
        'role_name', v_role_name,
        'removed_at', NOW()
      ),
      '/account/roles'
    );
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$
;

-- !section
-- section TRIGGERS
-- anchor auth_users_assign_customer_role
CREATE TRIGGER auth_users_assign_customer_role
AFTER INSERT ON auth.users FOR EACH ROW
EXECUTE FUNCTION app_access.assign_customer_role_on_new_user ()
;

-- anchor user_role_notify_change
CREATE TRIGGER user_role_notify_change
AFTER INSERT OR DELETE ON app_access.user_role FOR EACH ROW
EXECUTE FUNCTION app_access.notify_role_change ()
;

-- !section
-- section RLS POLICIES
-- anchor role
ALTER TABLE app_access.role ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "role_select" ON app_access.role FOR
SELECT
  USING (TRUE)
;

-- anchor capability
ALTER TABLE app_access.capability ENABLE ROW LEVEL SECURITY
;

-- anchor role_capability
ALTER TABLE app_access.role_capability ENABLE ROW LEVEL SECURITY
;

-- anchor user_role
ALTER TABLE app_access.user_role ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "user_role_manage" ON app_access.user_role FOR ALL USING (
  (
    SELECT
      app_access.has_capability ('access.user_role.manage')
  )
)
WITH
  CHECK (TRUE)
;

-- !section
-- section ROLES
-- anchor admin
INSERT INTO
  app_access.role (NAME, description)
VALUES
  (
    'admin',
    'Administrator role with full access'
  ),
  (
    'customer',
    'Customer role for users who are customers'
  ),
  (
    'provider',
    'Provider role for service providers'
  ),
  (
    'provider_applicant',
    'Role for users who have started a provider application'
  ),
  (
    'provider_applicant_under_review',
    'Role for users who have submitted a provider application and are under review'
  ),
  (
    'support_agent',
    'Role for users who are support agents'
  ),
  (
    'celebrity',
    'Celebrity role with specific content creation capabilities'
  )
;

-- !section
-- section CAPABILITIES
-- anchor admin
SELECT
  app_access.define_role_capability (
    'admin',
    ARRAY[
      'access.role.grant',
      'access.role.revoke',
      'access.user_role.manage'
    ]
  )
;

-- !section