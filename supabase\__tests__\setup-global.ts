import { User } from "@supabase/supabase-js";
import { dbClient, serviceClient } from "./utils/client";
import {
  getRateLimit,
  resetRateLimits,
  setRateLimit
} from "./utils/rate-limit";
import { spawn, ChildProcess } from "child_process";
import net from "net";

function isPortInUse(port: number): Promise<boolean> {
  return new Promise((resolve) => {
    const server = net.createServer();

    server.listen(port, () => {
      server.once("close", () => {
        resolve(false);
      });
      server.close();
    });

    server.on("error", () => {
      resolve(true);
    });
  });
}

async function setupRateLimiting(): Promise<number> {
  const originalMaxRequestsPerMinute = await getRateLimit();
  await resetRateLimits();
  await setRateLimit(100000000);
  return originalMaxRequestsPerMinute;
}

async function cleanupRateLimiting(
  originalMaxRequestsPerMinute: number
): Promise<void> {
  await resetRateLimits();
  await setRateLimit(originalMaxRequestsPerMinute);
}

async function setupApiServer(): Promise<ChildProcess | null> {
  const port3001InUse = await isPortInUse(3001);

  if (!port3001InUse) {
    console.log("Port 3001 is not in use, starting API dev server...");
    const apiProcess = spawn("pnpm", ["--filter", "api", "dev"], {
      stdio: "ignore", // Prevent API server logs
      shell: true
    });

    // Wait a moment for the server to start
    await new Promise((resolve) => setTimeout(resolve, 2000));
    return apiProcess;
  } else {
    console.log("Port 3001 is already in use, skipping API server start");
    return null;
  }
}

function cleanupApiServer(apiProcess: ChildProcess | null): void {
  if (apiProcess && !apiProcess.killed) {
    console.log("Stopping API dev server...");
    apiProcess.kill();
  }
}

async function getUsersSnapshot() {
  const {
    data: { users }
  } = await serviceClient.auth.admin.listUsers();
  return users;
}

function checkLeftoverUsers(previousUsers: User[], finalUsers: User[]): void {
  const leftOverUsers = finalUsers.length - previousUsers.length;

  if (leftOverUsers > 0) {
    console.error(
      `<<< ATTENTION: ${leftOverUsers} leftover users. Debug whether this is a database or test issue. Auto-cleanup is running, but please fix the issue to avoid this message. >>>`.toUpperCase()
    );
  }
}

export default async function globalSetup() {
  await dbClient.connect();

  const apiProcess = await setupApiServer();

  const originalMaxRequestsPerMinute = await setupRateLimiting();

  const previousUsers = await getUsersSnapshot();

  return async () => {
    await cleanupRateLimiting(originalMaxRequestsPerMinute);

    const finalUsers = await getUsersSnapshot();
    checkLeftoverUsers(previousUsers, finalUsers);

    cleanupApiServer(apiProcess);

    await dbClient.end();
  };
}
